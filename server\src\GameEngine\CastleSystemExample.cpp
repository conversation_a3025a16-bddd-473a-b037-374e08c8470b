// CastleSystemExample.cpp - 城堡系统示例程序
#include "CastleManager.h"
#include "GuildManager.h"
#include "../Common/Logger.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace MirServer;

void PrintCastleInfo(Castle* castle) {
    if (!castle) {
        std::cout << "城堡不存在！" << std::endl;
        return;
    }

    std::cout << "=== 城堡信息 ===" << std::endl;
    std::cout << "城堡名称: " << castle->GetCastleName() << std::endl;
    std::cout << "拥有者行会: " << (castle->GetOwnerGuild().empty() ? "无" : castle->GetOwnerGuild()) << std::endl;
    std::cout << "地图名称: " << castle->GetMapName() << std::endl;
    std::cout << "回城坐标: (" << castle->GetHomeX() << ", " << castle->GetHomeY() << ")" << std::endl;
    std::cout << "战争状态: ";
    switch (castle->GetWarStatus()) {
        case CastleWarStatus::PEACE: std::cout << "和平"; break;
        case CastleWarStatus::PREPARING: std::cout << "准备中"; break;
        case CastleWarStatus::ACTIVE: std::cout << "战争中"; break;
        case CastleWarStatus::ENDING: std::cout << "结束中"; break;
    }
    std::cout << std::endl;
    std::cout << "是否被攻击: " << (castle->IsUnderAttack() ? "是" : "否") << std::endl;
    std::cout << "总金币: " << castle->GetTotalGold() << std::endl;
    std::cout << "今日收入: " << castle->GetTodayIncome() << std::endl;
    std::cout << "技术等级: " << castle->GetTechLevel() << std::endl;
    std::cout << "力量值: " << castle->GetPower() << std::endl;
    std::cout << "攻击者列表: " << castle->GetAttackerList() << std::endl;
    std::cout << "================" << std::endl << std::endl;
}

void DemonstrateCastleBasics() {
    std::cout << "\n=== 城堡基础功能演示 ===" << std::endl;

    CastleManager& manager = CastleManager::GetInstance();
    Castle* castle = manager.FindCastle("Sabuk");

    if (!castle) {
        std::cout << "未找到沙巴克城堡！" << std::endl;
        return;
    }

    std::cout << "1. 初始城堡状态:" << std::endl;
    PrintCastleInfo(castle);

    std::cout << "2. 添加收入:" << std::endl;
    castle->IncomeGold(5000);
    std::cout << "添加了5000金币收入" << std::endl;
    PrintCastleInfo(castle);

    std::cout << "3. 设置技术等级和力量:" << std::endl;
    castle->SetTechLevel(3);
    castle->SetPower(200);
    std::cout << "设置技术等级为3，力量值为200" << std::endl;
    PrintCastleInfo(castle);
}

void DemonstrateAttackerManagement() {
    std::cout << "\n=== 攻击者管理演示 ===" << std::endl;

    CastleManager& manager = CastleManager::GetInstance();
    Castle* castle = manager.FindCastle("Sabuk");

    if (!castle) {
        std::cout << "未找到Sabuk城堡！" << std::endl;
        return;
    }

    MirServer::DWORD currentTime = MirServer::GetCurrentTime();

    std::cout << "1. 添加攻击者:" << std::endl;
    castle->AddAttacker("霸者行会", currentTime);
    castle->AddAttacker("战神行会", currentTime);
    castle->AddAttacker("龙族行会", currentTime);
    std::cout << "添加了3个攻击者行会" << std::endl;
    PrintCastleInfo(castle);

    std::cout << "2. 检查攻击者:" << std::endl;
    std::cout << "霸者行会是否为攻击者: " << (castle->IsAttacker("霸者行会") ? "是" : "否") << std::endl;
    std::cout << "和平行会是否为攻击者: " << (castle->IsAttacker("和平行会") ? "是" : "否") << std::endl;

    std::cout << "3. 移除攻击者:" << std::endl;
    castle->RemoveAttacker("战神行会");
    std::cout << "移除了战神行会" << std::endl;
    PrintCastleInfo(castle);
}

void DemonstrateWarSystem() {
    std::cout << "\n=== 战争系统演示 ===" << std::endl;

    CastleManager& manager = CastleManager::GetInstance();
    Castle* castle = manager.FindCastle("Sabuk");

    if (!castle) {
        std::cout << "未找到Sabuk城堡！" << std::endl;
        return;
    }

    std::cout << "1. 检查是否可以开始战争:" << std::endl;
    std::cout << "可以开始战争: " << (castle->CanStartWar() ? "是" : "否") << std::endl;

    if (castle->CanStartWar()) {
        std::cout << "2. 开始城堡战争:" << std::endl;
        castle->StartWar();
        std::cout << "城堡战争已开始！" << std::endl;
        PrintCastleInfo(castle);

        std::cout << "3. 模拟战争进行中..." << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(2));

        std::cout << "4. 停止城堡战争:" << std::endl;
        castle->StopWar();
        std::cout << "城堡战争已停止！" << std::endl;
        PrintCastleInfo(castle);
    }
}

void DemonstrateDefenseSystem() {
    std::cout << "\n=== 防御系统演示 ===" << std::endl;

    CastleManager& manager = CastleManager::GetInstance();
    Castle* castle = manager.FindCastle("Sabuk");

    if (!castle) {
        std::cout << "未找到Sabuk城堡！" << std::endl;
        return;
    }

    std::cout << "1. 修复城门:" << std::endl;
    bool result = castle->RepairDoor();
    std::cout << "修复城门结果: " << (result ? "成功" : "失败") << std::endl;

    std::cout << "2. 修复城墙:" << std::endl;
    result = castle->RepairWall(0); // 左墙
    std::cout << "修复左墙结果: " << (result ? "成功" : "失败") << std::endl;
    result = castle->RepairWall(1); // 中墙
    std::cout << "修复中墙结果: " << (result ? "成功" : "失败") << std::endl;
    result = castle->RepairWall(2); // 右墙
    std::cout << "修复右墙结果: " << (result ? "成功" : "失败") << std::endl;

    std::cout << "3. 控制城门:" << std::endl;
    castle->MainDoorControl(false); // 关闭城门
    std::cout << "城门已关闭" << std::endl;
    castle->MainDoorControl(true);  // 打开城门
    std::cout << "城门已打开" << std::endl;
}

void DemonstrateCastleManager() {
    std::cout << "\n=== 城堡管理器演示 ===" << std::endl;
    Logger::Info("开始城堡管理器演示");
    CastleManager& manager = CastleManager::GetInstance();

    std::cout << "1. 城堡数量: " << manager.GetCastleCount() << std::endl;
    Logger::Info("城堡数量: %d", manager.GetCastleCount());

    std::cout << "2. 城堡名称列表:" << std::endl;
    std::vector<std::string> nameList;
    manager.GetCastleNameList(nameList);
    for (const auto& name : nameList) {
        std::cout << "  - " << name << std::endl;
    }

    std::cout << "3. 城堡金币信息:" << std::endl;
    std::vector<std::string> goldInfo;
    manager.GetCastleGoldInfo(goldInfo);
    for (const auto& info : goldInfo) {
        std::cout << "  " << info << std::endl;
    }

    std::cout << "4. 全局收入分配:" << std::endl;
    manager.IncomeGold(10210);
    std::cout << "分配了10210金币给所有城堡" << std::endl;

    manager.GetCastleGoldInfo(goldInfo);
    for (const auto& info : goldInfo) {
        std::cout << "  " << info << std::endl;
    }
}

void DemonstrateSaveAndLoad() {
    std::cout << "\n=== 保存和加载演示 ===" << std::endl;

    CastleManager& manager = CastleManager::GetInstance();
    Castle* castle = manager.FindCastle("Sabuk");

    if (!castle) {
        std::cout << "未找到Sabuk城堡！" << std::endl;
        return;
    }

    std::cout << "1. 修改城堡数据:" << std::endl;
    castle->IncomeGold(8888);
    castle->SetTechLevel(5);
    castle->SetPower(500);
    castle->AddAttacker("测试行会", MirServer::GetCurrentTime());
    std::cout << "修改了城堡的金币、技术等级、力量值和攻击者" << std::endl;
    PrintCastleInfo(castle);

    std::cout << "2. 保存城堡数据:" << std::endl;
    castle->Save();
    manager.Save();
    std::cout << "城堡数据已保存" << std::endl;

    std::cout << "3. 运行城堡管理器:" << std::endl;
    manager.Run();
    std::cout << "城堡管理器运行完成" << std::endl;
}

int main() {
    std::cout << "城堡系统示例程序" << std::endl;
    std::cout << "==================" << std::endl;

    // 初始化日志系统
    Logger::SetLogFile("castle_example.log");
    Logger::SetLogLevel(LogLevel::LOG_INFO);

    try {
        // 初始化行会管理器（城堡系统依赖）
        std::cout << "初始化行会管理器..." << std::endl;
        GuildManager::GetInstance().Initialize();

        // 初始化城堡管理器
        std::cout << "初始化城堡管理器..." << std::endl;
        CastleManager::GetInstance().Initialize();

        // 演示各种功能
        DemonstrateCastleBasics();
        DemonstrateAttackerManagement();
        DemonstrateWarSystem();
        DemonstrateDefenseSystem();
        DemonstrateCastleManager();
        DemonstrateSaveAndLoad();

        std::cout << "\n=== 演示完成 ===" << std::endl;
        std::cout << "所有城堡系统功能演示完成！" << std::endl;

        // 清理
        CastleManager::GetInstance().Finalize();
        GuildManager::GetInstance().Finalize();

    } catch (const std::exception& e) {
        std::cerr << "发生异常: " << e.what() << std::endl;
        return 1;
    }

    std::cout << "\n按任意键退出..." << std::endl;
    std::cin.get();

    return 0;
}
